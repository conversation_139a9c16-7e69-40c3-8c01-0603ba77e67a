#ifndef CONFIG_H
#define CONFIG_H

#include <QString>
#include <QSize>

class Config {
public:
    // API配置
    static QString getApiBaseUrl() {
#ifdef QT_DEBUG
        return "http://localhost:8008";
#else
        return "https://api.yourdomain.com";
#endif
    }

    static QString getAuthServiceUrl() {
#ifdef QT_DEBUG
        return "http://localhost:8008";
#else
        return "https://auth.yourdomain.com";
#endif
    }

    // 应用配置
    static QString getAppName() {
        return "游戏客户端";
    }

    static QString getAppVersion() {
        return "1.0.0";
    }

    // 网络配置
    static int getRequestTimeout() {
        return 30000; // 30秒
    }

    static int getMaxRetries() {
        return 3;
    }

    // UI配置
    static QString getDefaultTheme() {
        return "dark";
    }

    static QSize getDefaultWindowSize() {
        return QSize(1200, 800);
    }

    static QSize getMinimumWindowSize() {
        return QSize(900, 600);
    }

    // 登录页面专用尺寸
    static QSize getLoginWindowSize() {
        return QSize(1000, 700);
    }

    // 布局比例设置
    static int getSidebarExpandedWidth() { return 180; } // 优化到180px，减少右侧空白
    static int getSidebarCollapsedWidth() { return 55; } // 增加到55px，确保按钮完整显示
    static double getMainContentRatio() { return 0.75; }
    static double getSidebarRatio() { return 0.25; }
    static double getServerListRatio() { return 0.65; }
    static double getServerDetailsRatio() { return 0.35; }

    // 间距设置
    static int getStandardSpacing() { return 20; }
    static int getCompactSpacing() { return 12; }
    static int getLargeSpacing() { return 30; }
    static int getStandardMargin() { return 25; }
    static int getCompactMargin() { return 15; }

    // 颜色主题
    static QString getPrimaryColor() { return "#4A90E2"; }
    static QString getSecondaryColor() { return "#7ED321"; }
    static QString getBackgroundColor() { return "#2C3E50"; }
    static QString getTextColor() { return "#ECF0F1"; }
    static QString getErrorColor() { return "#E74C3C"; }
    static QString getSuccessColor() { return "#27AE60"; }
    static QString getWarningColor() { return "#F39C12"; }
    static QString getHoverColor() { return "#34495E"; }
};

#endif // CONFIG_H
