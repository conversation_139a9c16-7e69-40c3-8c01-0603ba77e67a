#ifndef SIDEBARWIDGET_H
#define SIDEBARWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QFrame>
#include <QScrollArea>
#include <QListWidget>
#include <QGroupBox>
#include <QPropertyAnimation>
#include <QGraphicsDropShadowEffect>
#include "authservice.h"

class SidebarWidget : public QWidget
{
    Q_OBJECT

public:
    explicit SidebarWidget(AuthService *authService, QWidget *parent = nullptr);

    void updateUserInfo(const AuthService::UserInfo &userInfo);
    void setCollapsed(bool collapsed);
    bool isCollapsed() const { return m_collapsed; }
    void setTheme(const QString &theme);

signals:
    void gameServersClicked();
    void friendsClicked();
    void settingsClicked();
    void profileClicked();
    void statisticsClicked();
    void helpClicked();
    void logoutClicked();

private slots:
    void onToggleCollapse();
    void onMenuItemClicked();

private:
    void setupUI();
    void setupUserProfile();
    void setupNavigationMenu();
    void setupQuickActions();
    void setupFooter();
    void applyStyles();
    void updateCollapseState();
    void updateAvatarSize(int size); // 新增：动态更新头像大小
    void forceUpdateUserInfo(); // 新增：强制更新用户信息显示
    void initializeButtonProperties(); // 新增：初始化按钮属性
    QPushButton* createMenuButton(const QString &text, const QString &icon, const QString &tooltip = QString());
    void addSeparator();
    void addMenuSeparator(QVBoxLayout *layout);

    AuthService *m_authService;

    // 主布局
    QVBoxLayout *m_mainLayout;
    QScrollArea *m_scrollArea;
    QWidget *m_contentWidget;
    QVBoxLayout *m_contentLayout;

    // 用户资料区域
    QWidget *m_userProfileWidget;
    QHBoxLayout *m_userProfileLayout;
    QWidget *m_avatarContainer; // 添加头像容器引用
    QLabel *m_avatarLabel;
    QWidget *m_userInfoWidget;
    QVBoxLayout *m_userInfoLayout;
    QLabel *m_usernameLabel;
    QLabel *m_statusLabel;
    QLabel *m_levelLabel;

    // 导航菜单
    QWidget *m_navigationGroup;
    QVBoxLayout *m_navigationLayout;
    QPushButton *m_gameServersButton;
    QPushButton *m_friendsButton;
    QPushButton *m_statisticsButton;

    // 个人功能按钮（已合并到导航菜单中）
    QPushButton *m_profileButton;
    QPushButton *m_settingsButton;
    QPushButton *m_helpButton;

    // 底部区域
    QWidget *m_footerWidget;
    QVBoxLayout *m_footerLayout;
    QPushButton *m_logoutButton;
    QPushButton *m_collapseButton;

    // 状态
    bool m_collapsed;
    int m_expandedWidth;
    int m_collapsedWidth;
    QString m_currentTheme;

    // 动画
    QPropertyAnimation *m_collapseAnimation;

    // 样式
    QString m_primaryColor;
    QString m_secondaryColor;
    QString m_backgroundColor;
    QString m_textColor;
    QString m_hoverColor;
    QString m_activeColor;
};

#endif // SIDEBARWIDGET_H
